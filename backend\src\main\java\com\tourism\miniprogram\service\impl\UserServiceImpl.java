package com.tourism.miniprogram.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.tourism.miniprogram.entity.User;
import com.tourism.miniprogram.mapper.UserMapper;
import com.tourism.miniprogram.service.UserService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * 用户服务实现类
 *
 * <AUTHOR> Team
 * @since 2023-12-01
 */
@Slf4j
@Service
public class UserServiceImpl extends ServiceImpl<UserMapper, User> implements UserService {

    @Override
    public User getUserByOpenid(String openid) {
        try {
            return baseMapper.selectByOpenid(openid);
        } catch (Exception e) {
            log.error("根据openid查询用户失败，openid: {}", openid, e);
            return null;
        }
    }

    @Override
    public boolean updateUserInfo(User user) {
        try {
            int result = baseMapper.updateUserInfo(user);
            return result > 0;
        } catch (Exception e) {
            log.error("更新用户信息失败，userId: {}", user.getUserId(), e);
            return false;
        }
    }
}
