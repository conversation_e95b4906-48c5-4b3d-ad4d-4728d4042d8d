package com.tourism.miniprogram.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.tourism.miniprogram.common.Result;
import com.tourism.miniprogram.entity.User;
import com.tourism.miniprogram.service.UserService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

/**
 * 用户控制器
 *
 * <AUTHOR> Team
 * @since 2023-12-01
 */
@Slf4j
@RestController
@RequestMapping("/user")
@Api(tags = "用户管理")
public class UserController {

    @Autowired
    private UserService userService;

    /**
     * 获取用户列表（分页查询）
     *
     * @param current  当前页码
     * @param size     每页大小
     * @param nickname 用户昵称（模糊查询）
     * @param region   用户地区（模糊查询）
     * @param phone    手机号码（模糊查询）
     * @return 用户列表
     */
    @GetMapping("/list")
    @ApiOperation(value = "获取用户列表", notes = "分页查询用户列表，支持按昵称、地区、手机号模糊搜索")
    public Result<IPage<User>> getUserList(
            @ApiParam(value = "当前页码", defaultValue = "1") @RequestParam(defaultValue = "1") Long current,
            @ApiParam(value = "每页大小", defaultValue = "10") @RequestParam(defaultValue = "10") Long size,
            @ApiParam(value = "用户昵称") @RequestParam(required = false) String nickname,
            @ApiParam(value = "用户地区") @RequestParam(required = false) String region,
            @ApiParam(value = "手机号码") @RequestParam(required = false) String phone) {
        try {
            Page<User> page = new Page<>(current, size);
            QueryWrapper<User> queryWrapper = new QueryWrapper<>();

            // 添加查询条件
            if (StringUtils.hasText(nickname)) {
                queryWrapper.like("nickname", nickname);
            }
            if (StringUtils.hasText(region)) {
                queryWrapper.like("region", region);
            }
            if (StringUtils.hasText(phone)) {
                queryWrapper.like("phone", phone);
            }

            // 按创建时间倒序排列
            queryWrapper.orderByDesc("created_at");

            IPage<User> userPage = userService.page(page, queryWrapper);
            return Result.success(userPage);
        } catch (Exception e) {
            log.error("获取用户列表失败", e);
            return Result.error("获取用户列表失败");
        }
    }

    /**
     * 获取用户信息
     *
     * @param userId 用户ID
     * @return 用户信息
     */
    @GetMapping("/{userId}")
    @ApiOperation(value = "获取用户信息", notes = "根据用户ID获取用户详细信息")
    public Result<User> getUserInfo(@ApiParam(value = "用户ID", required = true) @PathVariable Integer userId) {
        try {
            User user = userService.getById(userId);
            if (user == null) {
                return Result.error("用户不存在");
            }
            return Result.success(user);
        } catch (Exception e) {
            log.error("获取用户信息失败，userId: {}", userId, e);
            return Result.error("获取用户信息失败");
        }
    }

    /**
     * 更新用户信息
     *
     * @param userId 用户ID
     * @param user   用户信息
     * @return 更新结果
     */
    @PutMapping("/{userId}")
    @ApiOperation(value = "更新用户信息", notes = "更新用户的基本信息")
    public Result<String> updateUserInfo(
            @ApiParam(value = "用户ID", required = true) @PathVariable Integer userId,
            @RequestBody User user) {
        try {
            user.setUserId(userId);
            boolean success = userService.updateUserInfo(user);
            if (success) {
                return Result.success("更新成功");
            } else {
                return Result.error("更新失败");
            }
        } catch (Exception e) {
            log.error("更新用户信息失败，userId: {}", userId, e);
            return Result.error("更新用户信息失败");
        }
    }
}
