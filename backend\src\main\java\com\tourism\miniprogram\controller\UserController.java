package com.tourism.miniprogram.controller;

import com.tourism.miniprogram.common.Result;
import com.tourism.miniprogram.entity.User;
import com.tourism.miniprogram.service.UserService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * 用户控制器
 *
 * <AUTHOR> Team
 * @since 2023-12-01
 */
@Slf4j
@RestController
@RequestMapping("/user")
@Api(tags = "用户管理")
public class UserController {

    @Autowired
    private UserService userService;

    /**
     * 获取用户信息
     *
     * @param userId 用户ID
     * @return 用户信息
     */
    @GetMapping("/{userId}")
    @ApiOperation(value = "获取用户信息", notes = "根据用户ID获取用户详细信息")
    public Result<User> getUserInfo(@ApiParam(value = "用户ID", required = true) @PathVariable Integer userId) {
        try {
            User user = userService.getById(userId);
            if (user == null) {
                return Result.error("用户不存在");
            }
            return Result.success(user);
        } catch (Exception e) {
            log.error("获取用户信息失败，userId: {}", userId, e);
            return Result.error("获取用户信息失败");
        }
    }

    /**
     * 更新用户信息
     *
     * @param userId 用户ID
     * @param user   用户信息
     * @return 更新结果
     */
    @PutMapping("/{userId}")
    @ApiOperation(value = "更新用户信息", notes = "更新用户的基本信息")
    public Result<String> updateUserInfo(
            @ApiParam(value = "用户ID", required = true) @PathVariable Integer userId,
            @RequestBody User user) {
        try {
            user.setUserId(userId);
            boolean success = userService.updateUserInfo(user);
            if (success) {
                return Result.success("更新成功");
            } else {
                return Result.error("更新失败");
            }
        } catch (Exception e) {
            log.error("更新用户信息失败，userId: {}", userId, e);
            return Result.error("更新用户信息失败");
        }
    }
}
