<template>
  <div class="sidebar-wrapper">
    <div class="sidebar-logo">
      <router-link to="/" class="sidebar-logo-link">
        <img src="/logo.png" alt="logo" class="sidebar-logo-img" />
        <h1 v-show="!isCollapse" class="sidebar-title">旅游管理后台</h1>
      </router-link>
    </div>
    
    <el-scrollbar wrap-class="scrollbar-wrapper">
      <el-menu
        :default-active="activeMenu"
        :collapse="isCollapse"
        :unique-opened="false"
        :collapse-transition="false"
        mode="vertical"
        background-color="#304156"
        text-color="#bfcbd9"
        active-text-color="#409EFF"
      >
        <SidebarItem
          v-for="route in routes"
          :key="route.path"
          :item="route"
          :base-path="route.path"
        />
      </el-menu>
    </el-scrollbar>
  </div>
</template>

<script setup>
import { computed } from 'vue'
import { useRoute } from 'vue-router'
import { useAppStore } from '@/stores/app'
import SidebarItem from './SidebarItem.vue'

const route = useRoute()
const appStore = useAppStore()

const isCollapse = computed(() => !appStore.sidebar.opened)
const activeMenu = computed(() => route.path)

// 路由配置
const routes = [
  {
    path: '/',
    redirect: '/dashboard',
    children: [
      {
        path: 'dashboard',
        name: 'Dashboard',
        meta: { title: '仪表盘', icon: 'House' }
      }
    ]
  },
  {
    path: '/user',
    redirect: '/user/list',
    meta: { title: '用户管理', icon: 'User' },
    children: [
      {
        path: 'list',
        name: 'UserList',
        meta: { title: '用户列表', icon: 'UserFilled' }
      }
    ]
  }
]
</script>

<style lang="scss" scoped>
.sidebar-wrapper {
  height: 100%;
  background-color: #304156;
  width: 210px !important;
  position: fixed;
  font-size: 0px;
  top: 0;
  bottom: 0;
  left: 0;
  z-index: 1001;
  overflow: hidden;
  transition: width 0.28s;
  
  .sidebar-logo {
    width: 100%;
    height: 50px;
    line-height: 50px;
    background: #2b2f3a;
    text-align: center;
    overflow: hidden;
    
    .sidebar-logo-link {
      height: 100%;
      width: 100%;
      display: flex;
      align-items: center;
      justify-content: center;
      text-decoration: none;
      
      .sidebar-logo-img {
        width: 32px;
        height: 32px;
        vertical-align: middle;
        margin-right: 12px;
      }
      
      .sidebar-title {
        display: inline-block;
        margin: 0;
        color: #fff;
        font-weight: 600;
        line-height: 50px;
        font-size: 14px;
        font-family: Avenir, Helvetica Neue, Arial, Helvetica, sans-serif;
        vertical-align: middle;
      }
    }
  }
  
  .el-menu {
    border: none;
    height: 100%;
    width: 100% !important;
  }
}

.hideSidebar .sidebar-wrapper {
  width: 54px !important;
}

.mobile .sidebar-wrapper {
  transition: transform 0.28s;
  width: 210px !important;
}

.mobile.hideSidebar .sidebar-wrapper {
  pointer-events: none;
  transition-duration: 0.3s;
  transform: translate3d(-210px, 0, 0);
}
</style>
