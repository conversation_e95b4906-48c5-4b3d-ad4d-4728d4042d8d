{"info": {"name": "旅游讲解小程序API测试", "description": "旅游讲解小程序后端API接口测试集合", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "认证管理", "item": [{"name": "健康检查", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/auth/test", "host": ["{{baseUrl}}"], "path": ["auth", "test"]}}, "response": []}, {"name": "微信小程序登录", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"code\": \"微信授权码\",\n  \"nickname\": \"测试用户\",\n  \"avatarUrl\": \"https://example.com/avatar.jpg\",\n  \"region\": \"北京市\",\n  \"phone\": \"13800138000\"\n}"}, "url": {"raw": "{{baseUrl}}/auth/wechat/login", "host": ["{{baseUrl}}"], "path": ["auth", "wechat", "login"]}}, "response": []}]}, {"name": "用户管理", "item": [{"name": "获取用户信息", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "{{token}}"}], "url": {"raw": "{{baseUrl}}/user/1", "host": ["{{baseUrl}}"], "path": ["user", "1"]}}, "response": []}, {"name": "更新用户信息", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "{{token}}"}], "body": {"mode": "raw", "raw": "{\n  \"nickname\": \"更新后的昵称\",\n  \"avatarUrl\": \"https://example.com/new-avatar.jpg\",\n  \"region\": \"上海市\",\n  \"phone\": \"13900139000\"\n}"}, "url": {"raw": "{{baseUrl}}/user/1", "host": ["{{baseUrl}}"], "path": ["user", "1"]}}, "response": []}]}], "variable": [{"key": "baseUrl", "value": "http://localhost:8080/api", "type": "string"}, {"key": "token", "value": "Bearer your_jwt_token_here", "type": "string"}]}