package com.tourism.miniprogram.service;

import cn.binarywang.wx.miniapp.api.WxMaService;
import cn.binarywang.wx.miniapp.bean.WxMaJscode2SessionResult;
import cn.hutool.core.util.StrUtil;
import com.tourism.miniprogram.dto.LoginResponse;
import com.tourism.miniprogram.dto.WechatLoginRequest;
import com.tourism.miniprogram.entity.User;
import com.tourism.miniprogram.util.JwtUtil;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.common.error.WxErrorException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;

/**
 * 微信服务类
 *
 * <AUTHOR> Team
 * @since 2023-12-01
 */
@Slf4j
@Service
public class WechatService {

    @Autowired
    private WxMaService wxMaService;

    @Autowired
    private UserService userService;

    @Autowired
    private JwtUtil jwtUtil;

    /**
     * 微信小程序登录
     *
     * @param request 登录请求
     * @return 登录响应
     */
    @Transactional(rollbackFor = Exception.class)
    public LoginResponse login(WechatLoginRequest request) {
        try {
            // 1. 调用微信接口获取session信息
            WxMaJscode2SessionResult sessionInfo = wxMaService.getUserService()
                    .getSessionInfo(request.getCode());
            
            if (sessionInfo == null || StrUtil.isBlank(sessionInfo.getOpenid())) {
                throw new RuntimeException("微信登录失败，无法获取用户信息");
            }

            String openid = sessionInfo.getOpenid();
            log.info("微信登录，openid: {}", openid);

            // 2. 查询用户是否存在
            User existUser = userService.getUserByOpenid(openid);
            boolean isNewUser = false;

            if (existUser == null) {
                // 3. 新用户，创建用户记录
                existUser = new User();
                existUser.setOpenid(openid);
                existUser.setNickname(request.getNickname());
                existUser.setAvatarUrl(request.getAvatarUrl());
                existUser.setRegion(request.getRegion());
                existUser.setPhone(request.getPhone());
                existUser.setCreatedAt(LocalDateTime.now());
                existUser.setDeleted(0);

                userService.save(existUser);
                isNewUser = true;
                log.info("创建新用户，userId: {}", existUser.getUserId());
            } else {
                // 4. 老用户，更新用户信息
                if (StrUtil.isNotBlank(request.getNickname())) {
                    existUser.setNickname(request.getNickname());
                }
                if (StrUtil.isNotBlank(request.getAvatarUrl())) {
                    existUser.setAvatarUrl(request.getAvatarUrl());
                }
                if (StrUtil.isNotBlank(request.getRegion())) {
                    existUser.setRegion(request.getRegion());
                }
                if (StrUtil.isNotBlank(request.getPhone())) {
                    existUser.setPhone(request.getPhone());
                }

                userService.updateById(existUser);
                log.info("更新用户信息，userId: {}", existUser.getUserId());
            }

            // 5. 生成JWT token
            String token = jwtUtil.generateToken(existUser.getUserId(), openid);

            // 6. 构建响应
            LoginResponse response = new LoginResponse();
            response.setUserId(existUser.getUserId());
            response.setToken(token);
            response.setNickname(existUser.getNickname());
            response.setAvatarUrl(existUser.getAvatarUrl());
            response.setRegion(existUser.getRegion());
            response.setPhone(existUser.getPhone());
            response.setIsNewUser(isNewUser);

            return response;

        } catch (WxErrorException e) {
            log.error("微信登录异常", e);
            throw new RuntimeException("微信登录失败：" + e.getError().getErrorMsg());
        } catch (Exception e) {
            log.error("登录处理异常", e);
            throw new RuntimeException("登录失败：" + e.getMessage());
        }
    }
}
