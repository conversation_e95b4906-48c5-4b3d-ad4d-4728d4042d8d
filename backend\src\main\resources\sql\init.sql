-- 创建数据库
CREATE DATABASE IF NOT EXISTS `tourism_miniprogram` DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

USE `tourism_miniprogram`;

-- 创建用户表
CREATE TABLE IF NOT EXISTS `users` (
  `user_id` int(11) NOT NULL AUTO_INCREMENT COMMENT '用户ID',
  `openid` varchar(100) NOT NULL COMMENT '微信openid',
  `nickname` varchar(50) DEFAULT NULL COMMENT '用户昵称',
  `avatar_url` varchar(200) DEFAULT NULL COMMENT '头像URL',
  `region` varchar(50) DEFAULT NULL COMMENT '用户地区',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `phone` varchar(255) DEFAULT NULL COMMENT '手机号码',
  `deleted` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否删除(0:未删除,1:已删除)',
  PRIMARY KEY (`user_id`),
  UNIQUE KEY `uk_openid` (`openid`),
  KEY `idx_created_at` (`created_at`),
  KEY `idx_deleted` (`deleted`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户表';

-- 插入测试数据
INSERT INTO `users` (`openid`, `nickname`, `avatar_url`, `region`, `phone`) VALUES
('test_openid_001', '测试用户1', 'https://example.com/avatar1.jpg', '北京市', '13800138001'),
('test_openid_002', '测试用户2', 'https://example.com/avatar2.jpg', '上海市', '13800138002');
