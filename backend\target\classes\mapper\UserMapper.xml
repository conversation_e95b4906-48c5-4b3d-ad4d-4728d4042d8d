<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tourism.miniprogram.mapper.UserMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.tourism.miniprogram.entity.User">
        <id column="user_id" property="userId" />
        <result column="openid" property="openid" />
        <result column="nickname" property="nickname" />
        <result column="avatar_url" property="avatarUrl" />
        <result column="region" property="region" />
        <result column="created_at" property="createdAt" />
        <result column="phone" property="phone" />
        <result column="deleted" property="deleted" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        user_id, openid, nickname, avatar_url, region, created_at, phone, deleted
    </sql>

    <!-- 根据openid查询用户 -->
    <select id="selectByOpenid" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM user
        WHERE openid = #{openid} AND deleted = 0
    </select>

    <!-- 更新用户信息 -->
    <update id="updateUserInfo">
        UPDATE user
        <set>
            <if test="nickname != null and nickname != ''">
                nickname = #{nickname},
            </if>
            <if test="avatarUrl != null and avatarUrl != ''">
                avatar_url = #{avatarUrl},
            </if>
            <if test="region != null and region != ''">
                region = #{region},
            </if>
            <if test="phone != null and phone != ''">
                phone = #{phone},
            </if>
        </set>
        WHERE user_id = #{userId} AND deleted = 0
    </update>

</mapper>
