<template>
  <div class="dashboard">
    <el-row :gutter="20">
      <!-- 统计卡片 -->
      <el-col :xs="24" :sm="12" :lg="6">
        <div class="stat-card">
          <div class="stat-icon user">
            <el-icon><User /></el-icon>
          </div>
          <div class="stat-content">
            <div class="stat-number">{{ stats.totalUsers }}</div>
            <div class="stat-label">总用户数</div>
          </div>
        </div>
      </el-col>
      
      <el-col :xs="24" :sm="12" :lg="6">
        <div class="stat-card">
          <div class="stat-icon today">
            <el-icon><Calendar /></el-icon>
          </div>
          <div class="stat-content">
            <div class="stat-number">{{ stats.todayUsers }}</div>
            <div class="stat-label">今日新增</div>
          </div>
        </div>
      </el-col>
      
      <el-col :xs="24" :sm="12" :lg="6">
        <div class="stat-card">
          <div class="stat-icon active">
            <el-icon><UserFilled /></el-icon>
          </div>
          <div class="stat-content">
            <div class="stat-number">{{ stats.activeUsers }}</div>
            <div class="stat-label">活跃用户</div>
          </div>
        </div>
      </el-col>
      
      <el-col :xs="24" :sm="12" :lg="6">
        <div class="stat-card">
          <div class="stat-icon wechat">
            <el-icon><ChatDotRound /></el-icon>
          </div>
          <div class="stat-content">
            <div class="stat-number">{{ stats.wechatUsers }}</div>
            <div class="stat-label">微信用户</div>
          </div>
        </div>
      </el-col>
    </el-row>
    
    <!-- 快捷操作 -->
    <el-row :gutter="20" style="margin-top: 20px;">
      <el-col :span="24">
        <el-card class="box-card">
          <template #header>
            <div class="card-header">
              <span>快捷操作</span>
            </div>
          </template>
          <div class="quick-actions">
            <el-button type="primary" @click="goToUserList">
              <el-icon><User /></el-icon>
              用户管理
            </el-button>
            <el-button type="success" @click="testConnection">
              <el-icon><Connection /></el-icon>
              测试连接
            </el-button>
            <el-button type="info" @click="viewDocs">
              <el-icon><Document /></el-icon>
              API文档
            </el-button>
          </div>
        </el-card>
      </el-col>
    </el-row>
    
    <!-- 最近用户 -->
    <el-row :gutter="20" style="margin-top: 20px;">
      <el-col :span="24">
        <el-card class="box-card">
          <template #header>
            <div class="card-header">
              <span>最近注册用户</span>
              <el-button type="text" @click="goToUserList">查看更多</el-button>
            </div>
          </template>
          <el-table :data="recentUsers" style="width: 100%">
            <el-table-column prop="userId" label="用户ID" width="80" />
            <el-table-column prop="nickname" label="昵称" />
            <el-table-column prop="region" label="地区" />
            <el-table-column prop="createdAt" label="注册时间">
              <template #default="scope">
                {{ formatDate(scope.row.createdAt) }}
              </template>
            </el-table-column>
            <el-table-column label="操作" width="120">
              <template #default="scope">
                <el-button type="text" size="small" @click="viewUser(scope.row.userId)">
                  查看
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { getUserList, testApi } from '@/api/user'
import { formatDate } from '@/utils'

const router = useRouter()

const stats = ref({
  totalUsers: 0,
  todayUsers: 0,
  activeUsers: 0,
  wechatUsers: 0
})

const recentUsers = ref([])

// 获取统计数据
const getStats = async () => {
  try {
    // 这里可以调用专门的统计接口，暂时用用户列表接口模拟
    const response = await getUserList({ current: 1, size: 10 })
    if (response.code === 200) {
      stats.value.totalUsers = response.data.total || 0
      stats.value.todayUsers = Math.floor(Math.random() * 10) // 模拟数据
      stats.value.activeUsers = Math.floor(stats.value.totalUsers * 0.8)
      stats.value.wechatUsers = stats.value.totalUsers
      recentUsers.value = response.data.records || []
    }
  } catch (error) {
    console.error('获取统计数据失败:', error)
  }
}

// 跳转到用户列表
const goToUserList = () => {
  router.push('/user/list')
}

// 查看用户详情
const viewUser = (userId) => {
  router.push(`/user/detail/${userId}`)
}

// 测试连接
const testConnection = async () => {
  try {
    const response = await testApi()
    if (response.code === 200) {
      ElMessage.success('连接测试成功')
    }
  } catch (error) {
    ElMessage.error('连接测试失败')
  }
}

// 查看API文档
const viewDocs = () => {
  window.open('http://localhost:8080/api/doc.html', '_blank')
}

onMounted(() => {
  getStats()
})
</script>

<style lang="scss" scoped>
.dashboard {
  padding: 20px;
}

.stat-card {
  background: #fff;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  margin-bottom: 20px;
  
  .stat-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 20px;
    
    .el-icon {
      font-size: 24px;
      color: #fff;
    }
    
    &.user {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    }
    
    &.today {
      background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    }
    
    &.active {
      background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    }
    
    &.wechat {
      background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
    }
  }
  
  .stat-content {
    .stat-number {
      font-size: 28px;
      font-weight: bold;
      color: #303133;
      line-height: 1;
    }
    
    .stat-label {
      font-size: 14px;
      color: #909399;
      margin-top: 8px;
    }
  }
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.quick-actions {
  display: flex;
  gap: 16px;
  flex-wrap: wrap;
  
  .el-button {
    display: flex;
    align-items: center;
    gap: 8px;
  }
}

@media (max-width: 768px) {
  .dashboard {
    padding: 10px;
  }
  
  .stat-card {
    padding: 15px;
    
    .stat-icon {
      width: 50px;
      height: 50px;
      margin-right: 15px;
      
      .el-icon {
        font-size: 20px;
      }
    }
    
    .stat-content .stat-number {
      font-size: 24px;
    }
  }
}
</style>
