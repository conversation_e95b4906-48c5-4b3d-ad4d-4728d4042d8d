<template>
  <div class="user-list">
    <!-- 搜索表单 -->
    <el-card class="search-card">
      <el-form :model="searchForm" :inline="true" class="search-form">
        <el-form-item label="用户昵称">
          <el-input
            v-model="searchForm.nickname"
            placeholder="请输入用户昵称"
            clearable
            style="width: 200px"
          />
        </el-form-item>
        <el-form-item label="用户地区">
          <el-input
            v-model="searchForm.region"
            placeholder="请输入用户地区"
            clearable
            style="width: 200px"
          />
        </el-form-item>
        <el-form-item label="手机号码">
          <el-input
            v-model="searchForm.phone"
            placeholder="请输入手机号码"
            clearable
            style="width: 200px"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">
            <el-icon><Search /></el-icon>
            搜索
          </el-button>
          <el-button @click="handleReset">
            <el-icon><Refresh /></el-icon>
            重置
          </el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 用户表格 -->
    <el-card class="table-card">
      <template #header>
        <div class="card-header">
          <span>用户列表</span>
          <el-button type="primary" @click="handleRefresh">
            <el-icon><Refresh /></el-icon>
            刷新
          </el-button>
        </div>
      </template>

      <el-table
        v-loading="loading"
        :data="userList"
        style="width: 100%"
        stripe
        border
      >
        <el-table-column prop="userId" label="用户ID" width="80" align="center" />
        
        <el-table-column label="头像" width="80" align="center">
          <template #default="scope">
            <el-avatar
              :size="40"
              :src="scope.row.avatarUrl"
              :alt="scope.row.nickname"
            >
              <el-icon><User /></el-icon>
            </el-avatar>
          </template>
        </el-table-column>
        
        <el-table-column prop="nickname" label="用户昵称" min-width="120">
          <template #default="scope">
            <span>{{ scope.row.nickname || '未设置' }}</span>
          </template>
        </el-table-column>
        
        <el-table-column prop="region" label="用户地区" min-width="100">
          <template #default="scope">
            <span>{{ scope.row.region || '未设置' }}</span>
          </template>
        </el-table-column>
        
        <el-table-column prop="phone" label="手机号码" min-width="120">
          <template #default="scope">
            <span>{{ scope.row.phone || '未绑定' }}</span>
          </template>
        </el-table-column>
        
        <el-table-column prop="createdAt" label="注册时间" min-width="160">
          <template #default="scope">
            {{ formatDate(scope.row.createdAt) }}
          </template>
        </el-table-column>
        
        <el-table-column label="微信状态" width="100" align="center">
          <template #default="scope">
            <el-tag type="success" size="small">
              <el-icon><Check /></el-icon>
              已绑定
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column label="操作" width="180" align="center" fixed="right">
          <template #default="scope">
            <el-button
              type="primary"
              size="small"
              @click="handleView(scope.row)"
            >
              <el-icon><View /></el-icon>
              查看
            </el-button>
            <el-button
              type="warning"
              size="small"
              @click="handleEdit(scope.row)"
            >
              <el-icon><Edit /></el-icon>
              编辑
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="pagination.current"
          v-model:page-size="pagination.size"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { getUserList } from '@/api/user'
import { formatDate, debounce } from '@/utils'

const router = useRouter()

// 搜索表单
const searchForm = reactive({
  nickname: '',
  region: '',
  phone: ''
})

// 分页信息
const pagination = reactive({
  current: 1,
  size: 10,
  total: 0
})

// 表格数据
const userList = ref([])
const loading = ref(false)

// 获取用户列表
const fetchUserList = async () => {
  loading.value = true
  try {
    const params = {
      current: pagination.current,
      size: pagination.size,
      ...searchForm
    }
    
    // 过滤空值
    Object.keys(params).forEach(key => {
      if (params[key] === '' || params[key] === null || params[key] === undefined) {
        delete params[key]
      }
    })
    
    const response = await getUserList(params)
    if (response.code === 200) {
      userList.value = response.data.records || []
      pagination.total = response.data.total || 0
    }
  } catch (error) {
    console.error('获取用户列表失败:', error)
    ElMessage.error('获取用户列表失败')
  } finally {
    loading.value = false
  }
}

// 防抖搜索
const debouncedFetch = debounce(fetchUserList, 300)

// 搜索
const handleSearch = () => {
  pagination.current = 1
  fetchUserList()
}

// 重置
const handleReset = () => {
  Object.keys(searchForm).forEach(key => {
    searchForm[key] = ''
  })
  pagination.current = 1
  fetchUserList()
}

// 刷新
const handleRefresh = () => {
  fetchUserList()
}

// 分页大小改变
const handleSizeChange = (size) => {
  pagination.size = size
  pagination.current = 1
  fetchUserList()
}

// 当前页改变
const handleCurrentChange = (current) => {
  pagination.current = current
  fetchUserList()
}

// 查看用户
const handleView = (row) => {
  router.push(`/user/detail/${row.userId}`)
}

// 编辑用户
const handleEdit = (row) => {
  router.push(`/user/edit/${row.userId}`)
}

onMounted(() => {
  fetchUserList()
})
</script>

<style lang="scss" scoped>
.user-list {
  .search-card {
    margin-bottom: 20px;
    
    .search-form {
      .el-form-item {
        margin-bottom: 0;
      }
    }
  }
  
  .table-card {
    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
    
    .pagination-container {
      margin-top: 20px;
      text-align: right;
    }
  }
}

@media (max-width: 768px) {
  .user-list {
    .search-form {
      .el-form-item {
        display: block;
        margin-bottom: 10px;
        
        .el-input {
          width: 100% !important;
        }
      }
    }
    
    .pagination-container {
      text-align: center;
      
      :deep(.el-pagination) {
        justify-content: center;
      }
    }
  }
}
</style>
