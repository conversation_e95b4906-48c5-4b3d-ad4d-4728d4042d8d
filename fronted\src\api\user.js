import request from '@/utils/request'

/**
 * 获取用户列表
 * @param {Object} params 查询参数
 * @param {number} params.current 当前页码
 * @param {number} params.size 每页大小
 * @param {string} params.nickname 用户昵称
 * @param {string} params.region 用户地区
 * @param {string} params.phone 手机号码
 */
export function getUserList(params) {
  return request({
    url: '/user/list',
    method: 'get',
    params
  })
}

/**
 * 获取用户详情
 * @param {number} userId 用户ID
 */
export function getUserInfo(userId) {
  return request({
    url: `/user/${userId}`,
    method: 'get'
  })
}

/**
 * 更新用户信息
 * @param {number} userId 用户ID
 * @param {Object} data 用户数据
 */
export function updateUser(userId, data) {
  return request({
    url: `/user/${userId}`,
    method: 'put',
    data
  })
}

/**
 * 微信登录
 * @param {Object} data 登录数据
 */
export function wechatLogin(data) {
  return request({
    url: '/auth/wechat/login',
    method: 'post',
    data
  })
}

/**
 * 测试接口
 */
export function testApi() {
  return request({
    url: '/auth/test',
    method: 'get'
  })
}
