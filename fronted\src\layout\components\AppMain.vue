<template>
  <section class="app-main">
    <transition name="fade-transform" mode="out-in">
      <router-view :key="key" />
    </transition>
  </section>
</template>

<script setup>
import { computed } from 'vue'
import { useRoute } from 'vue-router'

const route = useRoute()
const key = computed(() => route.path)
</script>

<style lang="scss" scoped>
.app-main {
  min-height: calc(100vh - 50px);
  width: 100%;
  position: relative;
  overflow: hidden;
  padding: 20px;
  margin-top: 50px;
}

.fixed-header + .app-main {
  padding-top: 70px;
}

.hasTagsView .app-main {
  min-height: calc(100vh - 84px);
}

.hasTagsView .fixed-header + .app-main {
  padding-top: 104px;
}
</style>
