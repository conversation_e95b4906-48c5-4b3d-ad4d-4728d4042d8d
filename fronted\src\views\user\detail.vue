<template>
  <div class="user-detail">
    <el-page-header @back="goBack" content="用户详情" />
    
    <el-card v-loading="loading" class="detail-card">
      <template #header>
        <div class="card-header">
          <span>用户信息</span>
          <el-button type="primary" @click="handleEdit">
            <el-icon><Edit /></el-icon>
            编辑用户
          </el-button>
        </div>
      </template>
      
      <div v-if="userInfo" class="user-info">
        <el-row :gutter="20">
          <!-- 用户头像和基本信息 -->
          <el-col :xs="24" :sm="8" :md="6">
            <div class="avatar-section">
              <el-avatar
                :size="120"
                :src="userInfo.avatarUrl"
                :alt="userInfo.nickname"
              >
                <el-icon><User /></el-icon>
              </el-avatar>
              <h3 class="username">{{ userInfo.nickname || '未设置昵称' }}</h3>
              <el-tag type="success" size="large">
                <el-icon><Check /></el-icon>
                微信用户
              </el-tag>
            </div>
          </el-col>
          
          <!-- 详细信息 -->
          <el-col :xs="24" :sm="16" :md="18">
            <div class="info-section">
              <el-descriptions :column="2" border>
                <el-descriptions-item label="用户ID">
                  <el-tag>{{ userInfo.userId }}</el-tag>
                </el-descriptions-item>
                
                <el-descriptions-item label="微信OpenID">
                  <el-text class="openid-text" type="info">
                    {{ userInfo.openid }}
                  </el-text>
                </el-descriptions-item>
                
                <el-descriptions-item label="用户昵称">
                  {{ userInfo.nickname || '未设置' }}
                </el-descriptions-item>
                
                <el-descriptions-item label="用户地区">
                  {{ userInfo.region || '未设置' }}
                </el-descriptions-item>
                
                <el-descriptions-item label="手机号码">
                  {{ userInfo.phone || '未绑定' }}
                </el-descriptions-item>
                
                <el-descriptions-item label="注册时间">
                  {{ formatDate(userInfo.createdAt) }}
                </el-descriptions-item>
                
                <el-descriptions-item label="头像地址" :span="2">
                  <el-link
                    v-if="userInfo.avatarUrl"
                    :href="userInfo.avatarUrl"
                    target="_blank"
                    type="primary"
                  >
                    {{ userInfo.avatarUrl }}
                  </el-link>
                  <span v-else>未设置</span>
                </el-descriptions-item>
              </el-descriptions>
            </div>
          </el-col>
        </el-row>
        
        <!-- 操作记录 -->
        <el-divider content-position="left">操作记录</el-divider>
        <div class="operation-logs">
          <el-timeline>
            <el-timeline-item
              timestamp="刚刚"
              placement="top"
            >
              <el-card>
                <h4>查看用户详情</h4>
                <p>管理员查看了用户详细信息</p>
              </el-card>
            </el-timeline-item>
            
            <el-timeline-item
              :timestamp="formatDate(userInfo.createdAt)"
              placement="top"
            >
              <el-card>
                <h4>用户注册</h4>
                <p>用户通过微信小程序完成注册</p>
              </el-card>
            </el-timeline-item>
          </el-timeline>
        </div>
      </div>
      
      <el-empty v-else description="用户信息不存在" />
    </el-card>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { ElMessage } from 'element-plus'
import { getUserInfo } from '@/api/user'
import { formatDate } from '@/utils'

const router = useRouter()
const route = useRoute()

const props = defineProps({
  id: {
    type: String,
    required: true
  }
})

const userInfo = ref(null)
const loading = ref(false)

// 获取用户详情
const fetchUserInfo = async () => {
  loading.value = true
  try {
    const response = await getUserInfo(props.id)
    if (response.code === 200) {
      userInfo.value = response.data
    } else {
      ElMessage.error('用户不存在')
      goBack()
    }
  } catch (error) {
    console.error('获取用户详情失败:', error)
    ElMessage.error('获取用户详情失败')
  } finally {
    loading.value = false
  }
}

// 返回上一页
const goBack = () => {
  router.back()
}

// 编辑用户
const handleEdit = () => {
  router.push(`/user/edit/${props.id}`)
}

onMounted(() => {
  fetchUserInfo()
})
</script>

<style lang="scss" scoped>
.user-detail {
  .detail-card {
    margin-top: 20px;
    
    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
  }
  
  .user-info {
    .avatar-section {
      text-align: center;
      padding: 20px;
      
      .username {
        margin: 15px 0 10px;
        color: #303133;
      }
    }
    
    .info-section {
      padding: 20px 0;
    }
    
    .openid-text {
      font-family: 'Courier New', monospace;
      font-size: 12px;
      word-break: break-all;
    }
    
    .operation-logs {
      margin-top: 20px;
    }
  }
}

@media (max-width: 768px) {
  .user-detail {
    .avatar-section {
      margin-bottom: 20px;
    }
    
    :deep(.el-descriptions) {
      .el-descriptions__label {
        width: 80px;
      }
    }
  }
}
</style>
