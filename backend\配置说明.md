# 旅游讲解小程序后端配置说明

## 1. 数据库配置

### 1.1 创建数据库
```sql
CREATE DATABASE tourism_miniprogram DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
```

### 1.2 执行初始化脚本
在MySQL中执行 `src/main/resources/sql/init.sql` 文件中的SQL语句。

### 1.3 修改数据库连接配置
编辑 `src/main/resources/application.yml` 文件：

```yaml
spring:
  datasource:
    url: ************************************************************************************************************************************************************
    username: 你的数据库用户名
    password: 你的数据库密码
```

## 2. 微信小程序配置

### 2.1 获取微信小程序信息
1. 登录微信公众平台：https://mp.weixin.qq.com/
2. 进入小程序管理后台
3. 在"开发" -> "开发管理" -> "开发设置"中获取：
   - AppID（小程序ID）
   - AppSecret（小程序密钥）

### 2.2 配置微信信息
编辑 `src/main/resources/application.yml` 文件：

```yaml
wechat:
  miniapp:
    app-id: 你的微信小程序AppID
    secret: 你的微信小程序AppSecret
```

## 3. 启动服务

### 3.1 使用Maven启动
```bash
mvn spring-boot:run
```

### 3.2 使用jar包启动
```bash
java -jar target/miniprogram-backend-1.0.0.jar
```

### 3.3 使用启动脚本（Windows）
双击 `start.bat` 文件

## 4. 验证服务

服务启动成功后，可以访问以下地址验证：

- **API文档**: http://localhost:8080/api/doc.html
- **数据库监控**: http://localhost:8080/api/druid/index.html (用户名: admin, 密码: 123456)
- **健康检查**: http://localhost:8080/api/auth/test

## 5. API测试

### 5.1 测试接口
```
GET http://localhost:8080/api/auth/test
```

预期响应：
```json
{
  "code": 200,
  "message": "操作成功",
  "data": "服务正常运行",
  "timestamp": 1701234567890
}
```

### 5.2 微信登录接口
```
POST http://localhost:8080/api/auth/wechat/login
Content-Type: application/json

{
  "code": "微信授权码",
  "nickname": "测试用户",
  "avatarUrl": "https://example.com/avatar.jpg",
  "region": "北京市",
  "phone": "13800138000"
}
```

## 6. 常见问题

### 6.1 数据库连接失败
- 检查数据库是否启动
- 检查数据库连接信息是否正确
- 检查数据库是否已创建

### 6.2 微信登录失败
- 检查微信小程序AppID和Secret是否正确
- 检查微信授权码是否有效（code只能使用一次）

### 6.3 端口被占用
如果8080端口被占用，可以在application.yml中修改：
```yaml
server:
  port: 8081
```

## 7. 生产环境部署建议

1. **修改JWT密钥**：使用更复杂的密钥
2. **关闭SQL日志**：在生产环境中关闭SQL日志输出
3. **配置日志文件**：确保日志文件路径可写
4. **数据库优化**：配置合适的连接池参数
5. **HTTPS配置**：在生产环境中使用HTTPS

## 8. 开发调试

### 8.1 查看日志
日志文件位置：`logs/miniprogram-backend.log`

### 8.2 数据库监控
访问 http://localhost:8080/api/druid/index.html 查看数据库连接池状态和SQL执行情况。

### 8.3 API文档
访问 http://localhost:8080/api/doc.html 查看完整的API文档，可以在线测试接口。
